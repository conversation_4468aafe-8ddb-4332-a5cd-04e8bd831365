import asyncio
import csv
from typing import List, Set
import random

from aiohttp import ClientResponseError
from xbox.webapi.api.client import XboxLiveClient
from xbox.webapi.authentication.manager import AuthenticationManager
from xbox.webapi.authentication.models import OAuth2TokenResponse
from xbox_panel.proxy_handler_client import init_proxy_pool, ProxySession
from xbox_panel.geolocator import get_location
from logging_utils import init_logger, logging
from xbox_panel.proxies import get_proxies

logger = init_logger()

START_INDEX_FILE = "xbox_panel/start_value.txt"
XUID_RANGE_START = ****************
XUID_RANGE_END = ****************
BATCH_SIZE = 50
RESULTS_FILE = "xbox_panel/found_accounts.csv"

async def get_clients(sessions):
    try:
        accounts = [
        ["0a475988-97c8-4120-bc0e-ec401e004a58", "****************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwAYA+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAfVFa2LL2kBaWrS/c+JjulStoCo9qRNGh0opFZUOE6GEUVXebyn+vAHjoks0b+D0ow4YQ46t4vBxNh0td5EYIfu09Qjp/LLXFLvg1mqwJyTTqIvvVc/GGcryQteSRobRwPYkr0sPOC9/eAbIPtJTa4WmvT3zf5HvfBCY63i6FjHqt2P28IeCO1pUaNENzWUMjdo0a4sUfrLRYADyey/dTPBxNLdX2eAieuUTg07IhNwZku707WbZEdhxrQd5iWBMPev7D/aBLkSubVeQ7fqlE8fn42E5+5qNKMXzJPA4ECHBNsfPW6AMe7DkuqSKRUg09ElbUUHwIUSux+SEC2xqMB4QZgAAEPlZ/f085qGbkFsAWqKJDa7gAWu39Eh9N/MsP4XS8+XwkUtP02wpjjNcM5w9j8XWLZlGa3/kYB4LE+Rs5+Rg+vCWe0C/B5FoUEsgkQJc8wvtxdvEI5I8/MVYgDpbKL3Al/3zNN+DUZBP+tfHpigwkpai4OFK0d62kIg8PI6mH6VZuRAwcZZDeOX+FZCAsiVR0U3PCxnv2Emm4KyWN5Rrv26p8aHH+3iVnNdCopFmhQHTKtqTjR6MEEHtEz5xMwgmexWPuPl9vTL/e/JtMDo1NHZ+RcfcDOIitvQeTieS6qTAeOwBlsMj2NPVst09ceTOHNNi7zt0Ys5llHufA8eTkQafstL3qsZtbs5IgA6hsQciD4ZoGAKwx0lEqGszCzcBkSoU7e/c1Ev2M5HjOFlvo3C3ZKnyBxK9xPcfC6Y907W4o/27/ng6P1koLU4aMGT2PhiTNEIpzhLOvrUKu53LcM9U8UxZ4YTTZB39f7FL6B7JG0G6X/r0Xlnv7+zMEOS0lmaKooFvTO9RSyzQbJ2m6nBAhAx4CrBNGKRY+dKbtohjmRM51hjpJyQUvJFBb/50zYJ/iD+1Zy2XRZubfsOIRY+VswnIhRxweBTtFFKscTfjTXD3jj/ViZ9qse/p8ezbPhQnUBbzrwzU9Hyewk7+xFOKFRsC", "refresh_token": "M.C514_BAY.0.U.-CmdlZNVwkUuQrrahbwQLhhuVhdc3TBxWAoO1LnmX8pCaMLe35tTM6bYileJneSwqpgWSsZ3ZF7wsJeQvh8ZKysFJxMFD3u2MuR4leLgBfuzVQ1Pyyxu!!EGFQduWz25SByyAInoqPyNzooBhzxrRxE!RU3hnwVE!17s2CvnnPizN!K!rjW!rEVkpuMurcFzb5AnuA9MkEXzk2I43TDabUJRTn4wtZaroGgsg5aenoU7eZkL3c8CEzD1hmx32lgeZ4B9ddgw*RZR9u15nvluLxgdqQcAkZwe2y3tTHd4m*p59XjlfD3enmbNgi77zEM172I0e0LJcbqLJ9CkDZlygxrs$", "user_id": "AAAAAAAAAAAAAAAAAAAAAGUrz5r7Ix6LnzgGwYrRUn4", "issued": "2024-10-25T00:33:23.559182+00:00"}],
        ["10d6ef0e-0d44-441c-8c8c-b122a30483a9", "*************************************", {"token_type": "bearer", "expires_in": 3600, "scope": "XboxLive.signin XboxLive.offline_access", "access_token": "EwA4A+pvBAAUKods63Ys1fGlwiccIFJ+qE1hANsAAbLRlrK52YdNVWxCbxcJqaHO8jzmTIQVkBrLrbpAWP7Cm2gucFYErb2W6hWZk4pvSDY5UBj5vJq8IYkU/bcqtxt3wzG/UFmlEiAgIAwbcvVHnsg7P6nDUcb6LQefvhUKusXrVqhapFbfX/TyKRo1nZaayMKZ6mlJswy4mLTSpO+3AwNbXiGjzjubcOHD+wZWXy5LwgPRDnTYMNKMcIRSfwSMQ9gMAsFLZBO+QUoOzZoWF1W8svW0V45jvgAVQ23QOHX2ooWA5IQMWng4QkAEwcQw7KAw1MLrQtiY+Qm/5eISr2VwPWUAMRbd/fdmpVMoObklc+xysZ67p1sltE1f/U0QZgAAECGUZNWCoVk55xh/gFBKNUEAAkz8ABHxeHONia1mhoT6aGPJpG20/V/7gO3vWcm44VeyUcG4BaEwW+qM4KdEMIBK3du5nBSfu/x44kPFl1AyNShGG59zE5RuJbgTtkeRVQjQ+x0QIzmaYfxu6ZWwWXD1cmRaVzZr1tXhhn7vfwKorVc0LBDPzuBAR5exMcR14TOEtmRB5eu0FDwjYhT96fZUWwhX2+PVfft/ABeRTgCp3hxtGp/aXk86yeZFgZDM8JC44Z3JoIDdQiyo8jFKsExpwxrBxD0nsX/4tpuC6jGY6lFb9+pkNOkNWG7LqQWsQj/VnPdXQ00LNYDPi+Bw0Lb/1X/zT+81gotmL3UYBsqttmBYIZ9y9l+lh2iR7EnDNbMC7kpefOVz9+GGN79b6MaywacDWg6edki7t6MHlZ3IjzFzrSHGkHBmDyJq6cWDLi54Sx8ldeBn/UjTUoWrzHf9cVCLdhrMPRZcu8d1GGS9nJI9sbbICpVbP4F6rGZ1qOqiAQJSUN262LzgzIVW+24hqEMHlSSJExvCX6P47Rbi1kGgja83sw3YNeSejO7H0byqeimPAbS7FlmmP3dstwtfGn3AiLbpZTi6tYhhdrc2JxAUeAFjuKOW6bthCKPqNy0hDdm83l25Bdtg1BUA4E/ss4fuF0XgUyK24qTB7/80zoybHdDDP9CLl9zUZFnNLn4yNgI=", "refresh_token": "M.C525_BAY.0.U.-CocDkJrLNmFQrfdByn15K39jxUNFfAqbOBVMUuP!NJ6YJm2CfHc2VHuD6qFJqS8*p2G0pQRRb09uwjPaKOnO2UhRbQ6tYub*K6kRaWZGlWPZEAZZNbjmYFeQh*SFEXTzYTZ51CcYjE2GzxhRb0N!5SpuAVHaVvh*QaWHcifXPrDA4H33xJnb6FJ0Uilwgq3ro1vrKBVNPbZwWNX6sDhXEwJiI!Lj4vfNwtlh8a0mJ0SAzJz*NT*KeFvi5wyjfY*Z6IUYQsgL6wevVI!fheIlnNFX2N4kTZlgYbB5MM8ZXCwqinKXtNs6E45J7hAeC6BvIaCOS4HJMDdr7QHDtfeDlpc$", "user_id": "AAAAAAAAAAAAAAAAAAAAAHHX_uuhT3hI3LCCMNEk56A", "issued": "2025-01-22T00:23:58.965565+00:00"}]
        ]
        clients = []

        count = -1
        for account in accounts:
            count += 1
            auth_mgr = AuthenticationManager(account[0], account[1], "", proxy_sessions=sessions)
            auth_mgr.oauth = OAuth2TokenResponse.parse_obj(account[2])
            try:
                await auth_mgr.refresh_tokens()
                clients.append(XboxLiveClient(auth_mgr))
                print(f"Added client {count}")
            except ClientResponseError:
                print(f"Could not refresh tokens for {count}")
                if hasattr(auth_mgr, 'session') and auth_mgr.session:
                    await auth_mgr.session.close()
        return clients
    
    except Exception as e:
        print(f"Error in get_clients: {e}")
        if sessions:
            for session in sessions:
                await session.close()
        raise

async def get_existing_xuids() -> Set[str]:
    existing_xuids = set()
    try:
        with open('xbox_panel/xuids.csv', 'r') as f:
            reader = csv.reader(f)
            for row in reader:
                if row:
                    existing_xuids.add(row[0])
    except FileNotFoundError:
        print("xuids.csv not found, starting with empty set")
    return existing_xuids

def read_start_index() -> int:
    try:
        with open(START_INDEX_FILE, 'r') as f:
            return int(f.read().strip())
    except (FileNotFoundError, ValueError):
        return XUID_RANGE_START

def save_start_index(index: int):
    with open(START_INDEX_FILE, 'w') as f:
        f.write(str(index))

def generate_xuid_batches(start_xuid: int, existing_xuids: Set[str], num_batches: int = 10) -> List[List[str]]:
    batches = []
    current_xuid = start_xuid

    for _ in range(num_batches):
        batch = []
        while len(batch) < BATCH_SIZE and current_xuid <= XUID_RANGE_END:
            xuid_str = str(current_xuid)
            if xuid_str not in existing_xuids:
                batch.append(xuid_str)
            current_xuid += 1

        if batch:
            batches.append(batch)
        else:
            break

    return batches, current_xuid

async def process_xuid_batch(client: XboxLiveClient, xuids: List[str], client_id: int, proxy_sessions=None) -> List[dict]:
    retry_count = 0
    
    while True:
        try:
            print(f"Client {client_id}: Processing batch of {len(xuids)} XUIDs (attempt {retry_count + 1})")
            
            proxy_session = None
            if proxy_sessions:
                proxy_session = random.choice(proxy_sessions)
            
            profile = await client.profile.get_profiles(xuids, session=proxy_session)
            break

        except ClientResponseError as e:
            if e.status == 429:
                print(f"Client {client_id}: Rate limited, sleeping for 10 seconds...")
                await asyncio.sleep(5)
                continue
            else:
                raise e
            
        except Exception as e:
            print(f"Client {client_id}: Error processing batch: {e}")
            retry_count += 1
            try:
                await proxy_session.close()
            except Exception as e:
                print(f"Client {client_id}: Error closing proxy session: {e}")
            
        
    found_accounts = []
    if not profile:
        return []

    for p in profile.profile_users:
        for setting in p.settings:
            if setting.id == 'Location':
                location = setting.value
                if location:
                    found_accounts.append({'xuid': p.id, 'location': location})
                break

    formatted_accounts = []
    for account in found_accounts:
        try:
            country_code = await get_location(account['location'])
            if country_code:
                formatted_accounts.append({
                    'xuid': account['xuid'],
                    'country_code': country_code
                })
        except Exception as e:
            print(f"Error getting location for {account['xuid']}: {e}")

    print(f"Client {client_id}: Found {len(formatted_accounts)} accounts with locations")
    print(f"Client {client_id}: Sleeping for 5 seconds...")
    await asyncio.sleep(5)
    return formatted_accounts

async def get_all_proxies():
    all_proxies_sessions = []
    proxies = await get_proxies()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['address'],
                "port": 4444,
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": "b03f165be1",
                    "password": "w1q4cGzT"
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True  # Set to True to prevent updating the proxy
        )
        all_proxies_sessions.append(proxy_session)

    print(f"running with {len(all_proxies_sessions)} proxies")
    return all_proxies_sessions

async def main(num_batches: int = 10):
    all_proxy_sessions = []
    clients = []
    
    try:
        print(f"Starting Xbox account discovery...")
        print(f"XUID range: {XUID_RANGE_START} - {XUID_RANGE_END}")
        all_proxy_sessions = await get_all_proxies()
        
        clients = await get_clients(sessions=all_proxy_sessions)
        
        if not clients:
            print("No clients available, exiting")
            return

        print("Loading existing XUIDs...")
        existing_xuids = await get_existing_xuids()

        start_xuid = read_start_index()
        print(f"Starting from XUID: {start_xuid}")

        batches, next_start_xuid = generate_xuid_batches(start_xuid, existing_xuids, num_batches)

        if not batches:
            print("No new XUIDs to process in the current range")
            return

        print(f"Generated {len(batches)} batches to process")

        all_found_accounts = []

        for i in range(0, len(batches), len(clients)):
            current_batches = batches[i:i + len(clients)]

            tasks = []
            for j, batch in enumerate(current_batches):
                client_id = j % len(clients)
                task = process_xuid_batch(clients[client_id], batch, client_id, all_proxy_sessions)
                tasks.append(task)

            print(f"Processing round {i//len(clients) + 1} with {len(tasks)} batches...")
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, Exception):
                    print(f"Error in batch processing: {result}")
                else:
                    all_found_accounts.extend(result)

        if all_found_accounts:
            print(f"Total accounts found: {len(all_found_accounts)}")

            for account in all_found_accounts:
                print(f"XUID: {account['xuid']}, Country: {account['country_code']}")
        else:
            print("No accounts found in this batch")

        save_start_index(next_start_xuid)
        print(f"Updated start index to: {next_start_xuid}")

    finally:
        try:
            if all_proxy_sessions:
                for session in all_proxy_sessions:
                    await session.close()
        except Exception as e:
            print(f"Error closing proxy sessions: {e}")

if __name__ == '__main__':
    asyncio.run(main(num_batches=10))
