import logging
import queue
from logging.handlers import Queue<PERSON><PERSON><PERSON>, QueueListener


class ColoredFormatter(logging.Formatter):

    # color codes is xterm 256 color numbers
    # color sheet can be found here: https://jonasjacek.github.io/colors/
    # color is written in format: '\u001b[38;5;' + color code + 'm'
    white = '\u001b[38;5;250m'
    yellow = '\u001b[38;5;226m'
    red = '\u001b[38;5;196m'
    cyan = '\u001b[38;5;44m'
    deep_pink3 = '\u001b[38;5;161m'
    reset = '\u001b[0m'
    format = '%(asctime)s | %(message)s'

    FORMATS = {
        logging.DEBUG: white + format + reset,
        logging.INFO: cyan + format + reset,
        logging.WARNING: yellow + format + reset,
        logging.ERROR: red + format + reset,
        logging.CRITICAL: deep_pink3 + format + reset
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)


def init_logger(logging_level=logging.DEBUG):
    # init logger
    stream_handler = logging.StreamHandler()
    # use custom Formatter to change colors of messages from default
    stream_handler.setFormatter(ColoredFormatter())
    log_queue = queue.SimpleQueue()
    queue_listener = QueueListener(log_queue, stream_handler)
    queue_listener.start()
    logger = logging.getLogger('main_logger')
    queue_handler = QueueHandler(log_queue)
    logger.addHandler(queue_handler)
    logger.setLevel(logging_level)
    return logger
