import aiohttp
import asyncio
import orjson

from panel.playstation.helpers.proxy_handler_client import ProxySession, RequestFailedException
from panel.playstation.helpers.auth_edit import Authenticator


class AsyncRequestBuilder:
    def __init__(self, authenticator):
        self.authenticator : Authenticator = authenticator
        self.country = 'US'
        self.language = 'en'
        self.default_headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, '
                                              'like Gecko) Chrome/90.0.4430.212 Safari/537.36'}

    async def get(self, session: ProxySession = None, **kwargs):
        access_token = await self.authenticator.obtain_fresh_access_token()

        headers = {
            **self.default_headers,
            'Authorization': f'Bearer {access_token}'
        }
        if 'headers' in kwargs.keys():
            headers = {**headers, **kwargs['headers']}

        params = None
        if 'params' in kwargs.keys():
            params = kwargs['params']

        data = None
        if 'data' in kwargs.keys():
            data = kwargs['data']
        if session is None:
            async with aiohttp.ClientSession() as session:
                response = await session.get(url=kwargs['url'], headers=headers, params=params, data=data,
                                             timeout=aiohttp.ClientTimeout(80))
                # response.raise_for_status()
                if response.status == 403:
                    reason = await response.text()
                    print(f'reason: {reason}')
                    raise RequestFailedException('403 Forbidden')
                
                return await response.json(loads=orjson.loads)
        else:
            response = await session.get(url=kwargs['url'], headers=headers, params=params, data=data)
            if response.status == 403:
                reason = await response.text()
                print(f'reason: {reason}')
                # When we ara getting a 403 error there is two possibilities.
                # It can be caused by PS API when privacy settings of an account to which we are making a request forbid
                # that request. In this case there will be error message with specific code. For purposes of this
                # project experimentally were determined codes for those endpoints:
                # - https://m.np.playstation.com/api/gamelist/v2/users/{account_id}/titles
                # - https://m.np.playstation.com/api/trophy/v1/users/{account_id}/trophyTitles
                # - https://m.np.playstation.net/api/userProfile/v1/internal/users/{account_id}/basicPresences
                # If there is none of this codes in response message we assume that it is caused by something else,
                # like proxy server.
                if '"code":2240526' in reason or '"code":3241984' in reason or '"code":2281486' in reason:
                    raise RequestFailedException('403 Forbidden by PS API because of privacy settings')
                raise RequestFailedException('403 Forbidden')
            # response.raise_for_status()
            return await response.json(loads=orjson.loads)

    async def post(self, session: ProxySession = None, **kwargs):
        access_token = await self.authenticator.obtain_fresh_access_token()
        headers = {
            **self.default_headers,
            'Authorization': 'Bearer {}'.format(access_token)
        }
        if 'headers' in kwargs.keys():
            headers = {**headers, **kwargs['headers']}

        json = None
        if 'json' in kwargs.keys():
            json = kwargs['json']

        data = None
        if 'data' in kwargs.keys():
            data = kwargs['data']
        if session is None:
            async with aiohttp.ClientSession() as session:
                async with session.post(url=kwargs['url'], headers=headers, json=json, data=data,
                                        timeout=aiohttp.ClientTimeout(80)) as response:
                    response.raise_for_status()
                    return await response.json()
        else:
            response = await session.post(url=kwargs['url'], headers=headers, json=json, data=data)
            if response.status == 403:
                raise RequestFailedException('403 Forbidden')
            response.raise_for_status()
            return await response.json()
