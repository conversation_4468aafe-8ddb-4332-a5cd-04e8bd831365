import logging
import aiohttp
import asyncio
import traceback
from typing import List, Optional
from aiohttp_proxy_connector import ProxyConnector
from config import PROXY_API_BACKEND
import random
import ujson
import ssl
from copy import deepcopy


class RequestFailedException(Exception):
    def __init__(self, error):
        self.error = error
        super().__init__()

    def __repr__(self):
        return f"{self.__class__.__name__}(Request failed with error: {self.error})"

    def __str__(self):
        return f"Request failed with error: {self.error}"


class ProxyUpdateFailedException(Exception):
    def __init__(self, error):
        self.error = error
        super().__init__()

    def __repr__(self):
        return (
            f"{self.__class__.__name__}(Proxy update failed with error: {self.error})"
        )

    def __str__(self):
        return f"Proxy update failed with error: {self.error}"


# refactor to use the new proxy client library
# & use the proxy session & api client from async_platform
class ProxySession:
    _session: aiohttp.ClientSession
    _ip_update_session: aiohttp.ClientSession
    _used_ip_set: set
    _failed_ip_set: set
    _proxy_filters: dict
    _proxy_url: str
    _ssl_allowed: bool
    _logger: logging.Logger
    _current_ip: str
    _is_ssl: bool
    _default_connect_timeout: int = 60
    _data: object
    _default_get_timeout: aiohttp.ClientTimeout

    def __init__(self) -> None:
        self._default_get_timeout = aiohttp.ClientTimeout(
            sock_connect=self._default_connect_timeout,
            sock_read=300,
            total=300 + self._default_connect_timeout,
        )
        self._default_ssl_context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_CLIENT)
        self._default_ssl_context.check_hostname = False
        self._default_ssl_context.verify_mode = ssl.VerifyMode.CERT_NONE

    def __getitem__(self, item):
        return self._data.get(item)

    @classmethod
    async def get_all(cls, proxy_params: dict) -> List:
        try:
            async with aiohttp.ClientSession() as session:
                response = await session.get(url=PROXY_API_BACKEND + "proxies/proxy")
                data = await response.json()
                if response.ok:
                    processed_data = []
                    for d in data:
                        d["proxy"]["unique_proxy_id"] = d["id"]

                        # skip residential
                        if d["proxy"]["proxy_type"] != "residential":
                            processed_data.append(d)
                    return processed_data
                else:
                    print("Proxy API response failed!")
                    return []
        except Exception as e:
            print(f"Error in get_all: {e}, traceback: {traceback.format_exc()}")

    @classmethod
    async def async_init(
        cls,
        logger=None,
        proxy_params: dict = None,
        failed_ip_set: set = None,
        used_ip_set: set = None,
    ):
        self = cls()
        if logger is None:
            logger = logging.getLogger()
        self._logger = logger
        proxy_params = proxy_params if proxy_params else {}
        self._ip_update_session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(limit=1, force_close=True)
        )
        self._ssl_allowed = False
        self._ssl_allowed = True
        self._session = aiohttp.ClientSession(
            connector=ProxyConnector(limit=0), json_serialize=ujson.dumps
        )
        self._used_ip_set = used_ip_set if used_ip_set is not None else set()
        self._failed_ip_set = failed_ip_set if failed_ip_set is not None else set()
        self._proxy_filters = proxy_params
        if self._ssl_allowed is False:
            self._proxy_filters["ssl"] = self._ssl_allowed
        self._current_ip = ""
        await self.update_proxy()
        return self

    @classmethod
    async def async_init_from_data(
        cls,
        logger,
        proxy_data: dict,
        failed_ip_set: set = None,
        used_ip_set: set = None,
        proxy_params: dict = None,
        do_not_update: bool = False,
    ):
        self = cls()
        self._data = proxy_data
        self._logger = logger
        proxy_params = proxy_params if proxy_params else {}
        if not do_not_update:
            self._ip_update_session = aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(limit=1, force_close=True)
            )
        self._ssl_allowed = False
        # there's a major performance penalty when using the custom proxy connector
        # self._session = aiohttp.ClientSession(
        #     connector=ProxyConnector(limit=0), json_serialize=ujson.dumps
        # )
        self._session = aiohttp.ClientSession(json_serialize=ujson.dumps)
        self._used_ip_set = used_ip_set if used_ip_set is not None else set()
        self._failed_ip_set = failed_ip_set if failed_ip_set is not None else set()
        self._proxy_filters = proxy_params
        if self._ssl_allowed is False:
            self._proxy_filters["ssl"] = self._ssl_allowed
        self._current_ip = ""
        self._set_proxy(data=proxy_data)
        return self

    def _set_proxy(self, data: dict):
        if self._current_ip:
            try:
                self._used_ip_set.remove(self._current_ip)
            except KeyError:
                pass
        self._is_ssl = data["proxy"]["ssl"]
        if data["proxy"]["protocol"] == "https" and not self._is_ssl:
            data["proxy"]["protocol"] = "http"
        self._proxy_url = f'{data["proxy"]["protocol"]}://'
        if data["provider"]["credentials"].get("username"):
            self._proxy_url += (
                f'{data["provider"]["credentials"]["username"]}:'
                f'{data["provider"]["credentials"]["password"]}@'
            )
        self._proxy_url += f'{data["proxy"]["ip"]}:{data["proxy"]["port"]}'
        self._current_ip = data["proxy"]["ip"]
        self._used_ip_set.add(self._current_ip)
        self._data = data

    async def update_proxy(self):
        post_data = {
            "proxy": {
                "filters": self._proxy_filters,
                "usedIps": list(self._failed_ip_set.union(self._used_ip_set)),
                "random": True,
            }
        }
        response = await self._ip_update_session.post(
            url=PROXY_API_BACKEND + "proxy/get", json=post_data
        )
        data = await response.json()
        if response.ok:
            self._set_proxy(data=data)
        else:
            raise ProxyUpdateFailedException(data["errors"])

    async def get(self, url: str, clear_session: bool = True, **kwargs):
        if clear_session:
            self._session.cookie_jar.clear()
            self._session.headers.clear()
        try:
            return await self._session.get(
                url,
                proxy=self._proxy_url,
                timeout=self._default_get_timeout,
                ssl=self._default_ssl_context,
                **kwargs,
            )
        except Exception as e:
            if isinstance(e, asyncio.TimeoutError):
                e.args = ("Connection timeout",)
            raise RequestFailedException(error=e)

    async def post(self, url: str, **kwargs):
        timeout = aiohttp.ClientTimeout(
            sock_connect=self._default_connect_timeout,
            sock_read=300,
            total=300 + self._default_connect_timeout,
        )
        self._session.cookie_jar.clear()
        self._session.headers.clear()
        context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_CLIENT)
        context.check_hostname = False
        context.verify_mode = ssl.VerifyMode.CERT_NONE
        request_proxy_url = self._proxy_url
        try:
            response = await self._session.post(
                url, proxy=request_proxy_url, timeout=timeout, ssl=context, **kwargs
            )
            return response
        except Exception as e:
            if isinstance(e, asyncio.TimeoutError):
                e.args = ("Connection timeout",)
            raise RequestFailedException(error=e)

    async def request(self, method, url: str, **kwargs):
        timeout = aiohttp.ClientTimeout(
            sock_connect=self._default_connect_timeout,
            sock_read=300,
            total=300 + self._default_connect_timeout,
        )
        self._session.cookie_jar.clear()
        self._session.headers.clear()
        context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_CLIENT)
        context.check_hostname = False
        context.verify_mode = ssl.VerifyMode.CERT_NONE
        request_proxy_url = self._proxy_url
        try:
            response = await self._session.request(
                method,
                url,
                proxy=request_proxy_url,
                timeout=timeout,
                ssl=context,
                **kwargs,
            )
            return response
        except Exception as e:
            if isinstance(e, asyncio.TimeoutError):
                e.args = ("Connection timeout",)
            raise RequestFailedException(error=e)

    async def close(self):
        if hasattr(self, "_ip_update_session"):
            await self._ip_update_session.close()
        if hasattr(self, "_session"):
            await self._session.close()


class ProxyPool:
    SUBREGIONS_BY_REGION = {
        "Asia": [
            "Southern Asia",
            "Western Asia",
            "South-eastern Asia",
            "Eastern Asia",
            "Central Asia",
        ],
        "Europe": [
            "Northern Europe",
            "Southern Europe",
            "Western Europe",
            "Eastern Europe",
        ],
        "Africa": ["Northern Africa", "Sub-Saharan Africa"],
        "Oceania": [
            "Polynesia",
            "Australia and New Zealand",
            "Melanesia",
            "Micronesia",
        ],
        "Americas": ["Latin America and the Caribbean", "Northern America"],
    }

    COUNTRIES_BY_SUBREGION = {
        "Southern Asia": ["AF", "BD", "BT", "IN", "IR", "MV", "NP", "PK", "LK"],
        "Northern Europe": [
            "AX",
            "DK",
            "EE",
            "FO",
            "FI",
            "GG",
            "IS",
            "IE",
            "IM",
            "JE",
            "LV",
            "LT",
            "NO",
            "SJ",
            "SE",
            "GB",
        ],
        "Southern Europe": [
            "AL",
            "AD",
            "BA",
            "HR",
            "GI",
            "GR",
            "VA",
            "IT",
            "MT",
            "ME",
            "MK",
            "PT",
            "SM",
            "RS",
            "SI",
            "ES",
        ],
        "Northern Africa": ["DZ", "EG", "LY", "MA", "SD", "TN", "EH"],
        "Polynesia": ["AS", "CK", "PF", "NU", "PN", "WS", "TK", "TO", "TV", "WF"],
        "Sub-Saharan Africa": [
            "AO",
            "BJ",
            "BW",
            "IO",
            "BF",
            "BI",
            "CV",
            "CM",
            "CF",
            "TD",
            "KM",
            "CG",
            "CD",
            "CI",
            "DJ",
            "GQ",
            "ER",
            "SZ",
            "ET",
            "TF",
            "GA",
            "GM",
            "GH",
            "GN",
            "GW",
            "KE",
            "LS",
            "LR",
            "MG",
            "MW",
            "ML",
            "MR",
            "MU",
            "YT",
            "MZ",
            "NA",
            "NE",
            "NG",
            "RE",
            "RW",
            "SH",
            "ST",
            "SN",
            "SC",
            "SL",
            "SO",
            "ZA",
            "SS",
            "TZ",
            "TG",
            "UG",
            "ZM",
            "ZW",
        ],
        "Latin America and the Caribbean": [
            "AI",
            "AG",
            "AR",
            "AW",
            "BS",
            "BB",
            "BZ",
            "BO",
            "BQ",
            "BV",
            "BR",
            "KY",
            "CL",
            "CO",
            "CR",
            "CU",
            "CW",
            "DM",
            "DO",
            "EC",
            "SV",
            "FK",
            "GF",
            "GD",
            "GP",
            "GT",
            "GY",
            "HT",
            "HN",
            "JM",
            "MQ",
            "MX",
            "MS",
            "NI",
            "PA",
            "PY",
            "PE",
            "PR",
            "BL",
            "KN",
            "LC",
            "MF",
            "VC",
            "SX",
            "GS",
            "SR",
            "TT",
            "TC",
            "UY",
            "VE",
            "VG",
            "VI",
        ],
        "Western Asia": [
            "AM",
            "AZ",
            "BH",
            "CY",
            "GE",
            "IQ",
            "IL",
            "JO",
            "KW",
            "LB",
            "OM",
            "PS",
            "QA",
            "SA",
            "SY",
            "TR",
            "AE",
            "YE",
        ],
        "Australia and New Zealand": ["AU", "CX", "CC", "HM", "NZ", "NF"],
        "Western Europe": ["AT", "BE", "FR", "DE", "LI", "LU", "MC", "NL", "CH"],
        "Eastern Europe": ["BY", "BG", "CZ", "HU", "MD", "PL", "RO", "RU", "SK", "UA"],
        "Northern America": ["BM", "CA", "GL", "PM", "US"],
        "South-eastern Asia": [
            "BN",
            "KH",
            "ID",
            "LA",
            "MY",
            "MM",
            "PH",
            "SG",
            "TH",
            "TL",
            "VN",
        ],
        "Eastern Asia": ["CN", "HK", "JP", "KP", "KR", "MO", "MN", "TW"],
        "Melanesia": ["FJ", "NC", "PG", "SB", "VU"],
        "Micronesia": ["GU", "KI", "MH", "FM", "NR", "MP", "PW", "UM"],
        "Central Asia": ["KZ", "KG", "TJ", "TM", "UZ"],
    }

    def __init__(
        self,
        pool_size: int,
        filter_providers: List[str] = None,
        logger: Optional[logging.Logger] = None,
        keep_providers: List[str] = None,
        unique_ips=False,
        countries: Optional[List[str]] = None
    ):
        self.pool_size = pool_size
        self.filter_providers = filter_providers
        self.keep_providers = keep_providers
        self.logger = logger
        self.sessions = []
        self.weights = []
        self.exclusive_session_map = {}
        self.unique_ips = unique_ips
        self.countries = countries

    async def init_pool(self):
        proxies = await self.get_all_proxies_filtered()

        provider_set = set([proxy["provider"]["name"] for proxy in proxies])
        self.logger.info(f"Providers: {provider_set}")
        sessions_to_create = len(proxies)
        if self.pool_size > 0:
            if len(proxies) < self.pool_size:
                log_str = (
                    f"Not enough proxies with given parameters to create session pool with requested size."
                    f"There are {len(proxies)} proxies, "
                    f'{"pool will not be created." if len(proxies) == 0 else "pool will be this size."}'
                )
                if self.logger:
                    self.logger.info(log_str)
                else:
                    print(log_str)
                self.pool_size = len(proxies)
            sessions_to_create = self.pool_size
        else:
            log_str = (
                f"There are {len(proxies)} proxies, "
                f'{"pool will not be created." if len(proxies) == 0 else "pool will be this size."}'
            )
            if self.logger:
                self.logger.info(log_str)
            else:
                print(log_str)

        self.sessions = await asyncio.gather(
            *[
                ProxySession.async_init_from_data(
                    proxy_data=proxies[i],
                    logger=logging.getLogger(),
                    proxy_params={},
                    do_not_update=True,
                )
                for i in range(sessions_to_create)
            ]
        )

        # calculate the weights
        for proxy_session in self.sessions:
            latency = proxy_session._data["proxy"]["latency"]
            self.weights.append(1 / latency)

    # TODO: refactor to remove code duplication
    async def update_pool(self):
        self.logger.info(f"update proxy pool, proxies before: {len(self.sessions)}")
        proxies = await self.get_all_proxies_filtered()

        # filter out proxies that are already being used
        used_proxies_ids = [s._data["proxy"]["unique_proxy_id"] for s in self.sessions]
        proxies = [
            p for p in proxies if p["proxy"]["unique_proxy_id"] not in used_proxies_ids
        ]

        sessions_to_create = len(proxies)
        if self.pool_size > 0:
            need_proxies = self.pool_size - len(self.sessions)
            if need_proxies > 0:
                sessions_to_create = need_proxies
            else:
                sessions_to_create = 0

        sessions = await asyncio.gather(
            *[
                ProxySession.async_init_from_data(
                    proxy_data=proxies[i],
                    logger=logging.getLogger(),
                    proxy_params={},
                    do_not_update=True,
                )
                for i in range(sessions_to_create)
            ]
        )
        weights = []
        for proxy_session in sessions:
            latency = proxy_session._data["proxy"]["latency"]
            weights.append(1 / latency)

        self.sessions += sessions
        self.weights += weights
        self.logger.info(f"proxies after: {len(self.sessions)}")

    async def get_all_proxies_filtered(self):
        proxies = await ProxySession.get_all(proxy_params={})
        if self.filter_providers:
            proxies = [
                proxy
                for proxy in proxies
                if proxy["provider"]["name"] not in self.filter_providers
            ]
        if self.keep_providers:
            proxies = [
                proxy
                for proxy in proxies
                if proxy["provider"]["name"] in self.keep_providers
            ]
        if self.countries:
            proxies = [
                proxy
                for proxy in proxies
                if proxy["proxy"]["country_code"] in self.countries
            ]
        if self.unique_ips:
            ips_set = set()
            filtered_proxies = []
            for p in proxies:
                ip = p["proxy"]["ip"]
                if ip not in ips_set:
                    filtered_proxies.append(p)
                    ips_set.add(ip)
            proxies = filtered_proxies
        return proxies

    async def close_pool(self):
        await asyncio.gather(*[s.close() for s in self.sessions])
        self.sessions = []
        self.weights = []

    def get_weighted_random_proxy(self):
        return random.choices(self.sessions, weights=self.weights, k=1)[0]

    def get_random_proxy(self):
        s = random.choice(self.sessions)
        return s

    def get_random_proxy_random_region(self):
        """
        get random session from random country from random region
        """
        country_proxy_map = {}
        for session in self.sessions:
            country = session._data["proxy"]["country_code"]
            if country_proxy_map.get(country):
                country_proxy_map[country].append(session)
            else:
                country_proxy_map[country] = [session]

        region_map = deepcopy(self.COUNTRIES_BY_SUBREGION)
        for region in region_map:
            region_map[region] = [
                c for c in region_map[region] if c in country_proxy_map
            ]

        non_empty_regions = [r for r in region_map if region_map[r]]
        if not non_empty_regions:
            self.logger.warning("no proxy regions to choose from")
            return None

        random_region = random.choice(non_empty_regions)
        random_country = random.choice(region_map[random_region])
        random_session = random.choice(country_proxy_map[random_country])
        return random_session

    async def remove_proxy(self, proxy_session):
        await proxy_session.close()
        session_index = self.sessions.index(proxy_session)
        del self.sessions[session_index]
        del self.weights[session_index]

    async def get_exclusive_session(self):
        # get coro name
        coro_name = asyncio.current_task().get_name()
        # if there's already a session taken
        if coro_name in self.exclusive_session_map:
            return self.exclusive_session_map[coro_name]
        else:
            # if no session taken
            # get all free sessions
            free_sessions = set(self.sessions) - set(
                self.exclusive_session_map.values()
            )
            if free_sessions:
                # book session
                session = free_sessions.pop()
                self.exclusive_session_map[coro_name] = session
                return session
            else:
                # no free sessions
                self.logger.error("No free sessions left to book")
                return None

    async def release_exclusive_session(self):
        # get coro name
        coro_name = asyncio.current_task().get_name()
        # delete regardless of whether it is in the dictionary
        self.exclusive_session_map.pop(coro_name, None)

    async def release_and_remove_exclusive_session(self, session):
        session_index = self.sessions.index(session)
        del self.sessions[session_index]
        del self.weights[session_index]

        # get coro name
        coro_name = asyncio.current_task().get_name()
        # delete regardless of whether it is in the dictionary
        self.exclusive_session_map.pop(coro_name, None)

        await session.close()


async def init_proxy_pool(
    session_pool: int,
    filter_providers: List[str] = None,
    logger: Optional[logging.Logger] = None,
) -> List[ProxySession]:
    while True:
        try:
            proxies = await ProxySession.get_all(proxy_params={})
            break
        except Exception as e:
            message = f"Error while trying to get proxies: {str(e)}. Retrying..."
            if logger:
                logger.critical(message)
            else:
                print(message)
    if filter_providers:
        proxies = [
            proxy
            for proxy in proxies
            if proxy["provider"]["name"] not in filter_providers
        ]
    if session_pool > 0:
        if len(proxies) < session_pool:
            log_str = (
                f"Not enough proxies with given parameters to create session pool with requested size."
                f"There are {len(proxies)} proxies, "
                f'{"pool will not be created." if len(proxies) == 0 else "pool will be this size."}'
            )
            if logger:
                logger.info(log_str)
            else:
                print(log_str)
            session_pool = len(proxies)
        random.shuffle(proxies)
        sessions = []
        for i in range(session_pool):
            sessions.append(
                await ProxySession.async_init_from_data(
                    proxy_data=proxies[i],
                    logger=logging.getLogger(),
                    proxy_params={},
                    do_not_update=True,
                )
            )
        return sessions
    else:
        log_str = (
            f"There are {len(proxies)} proxies, "
            f'{"pool will not be created." if len(proxies) == 0 else "pool will be this size."}'
        )
        if logger:
            logger.info(log_str)
        else:
            print(log_str)
        random.shuffle(proxies)
        sessions = []
        for i in range(len(proxies)):
            sessions.append(
                await ProxySession.async_init_from_data(
                    proxy_data=proxies[i],
                    logger=logging.getLogger(),
                    proxy_params={},
                    do_not_update=True,
                )
            )
        return sessions


if __name__ == "__main__":

    async def main():
        proxy_pool = ProxyPool(
            -1,
            filter_providers=["hola", "teraVPN", "soax", "hola_premium"],
            logger=logging.getLogger(),
        )
        await proxy_pool.init_pool()
        session = proxy_pool.get_random_proxy_random_region()
        print(session)

        await proxy_pool.close_pool()

    asyncio.run(main())
