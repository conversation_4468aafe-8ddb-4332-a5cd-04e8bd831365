import query_builder as qb
import time
from datetime import datetime, timedelta

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')
ps_metrics = qb.Schema('metrics', db_name='ps')

monitored_file = 'panel/playstation/monitored_account.txt'
last_checked = 'panel/playstation/last_id_checked.txt'


async def get_starting_account_id():
    try:
        with open(last_checked, 'r') as f:
            return int(f.read().strip())
    except FileNotFoundError:
        return 0

async def save_last_checked(account_id):
    with open(last_checked, 'w') as f:
        f.write(str(account_id))

async def get_n_accounts(batch_size=10, starting_account_id=0):
    # gets batch_size amounts of accounts starting at starting_account_id
    starting_account_id = await get_starting_account_id()
    players_table = ps_public.players
    monitored_table = ps_metrics.daily_monitored_25_08_05

    subq = qb.select(monitored_table.c.account_id)

    query = qb.Query(
        qb.select(players_table.c.account_id)
        .where(
            players_table.c.account_id > starting_account_id,
            players_table.c.account_id.not_in(subq)
        )
        .order_by(players_table.c.account_id.asc())
        .limit(batch_size)
    )
    results = await query.fetchall()
    account_ids = [r['account_id'] for r in results]

    await save_last_checked(account_ids[-1])
    return account_ids

async def get_tokens(number_of_tokens=1):
    table = ps_public.accounts_sso
    query = qb.Query(
        qb.select(table.c.sso)
        .where(
            table.c.updated_at > datetime.now() - timedelta(days=10)
        )
        .limit(number_of_tokens)
    )
    results = await query.fetchall()
    return [r['sso'] for r in results]

async def main():
    print("starting")
    t0 = time.time()
    # accounts = await get_n_accounts(batch_size=10000)
    # print(accounts[:5])

    tokens = await get_tokens(number_of_tokens=10)
    print(tokens)
    
    t1 = time.time()
    print(f"Time: {t1-t0}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
