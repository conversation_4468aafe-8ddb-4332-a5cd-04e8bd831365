from panel.playstation.get_unmonitored_accounts import get_n_accounts, get_tokens
import asyncio
import asyncio
from asyncio import Queue, QueueEmpty
import time

import uvloop

from panel.playstation.helpers.auth_edit import Authenticator
from panel.playstation.helpers.async_request_builder import AsyncRe<PERSON><PERSON>uilder
from panel.playstation.helpers.proxy_handler_client import ProxyPool, RequestFailedException, ProxySession
from aiohttp.client_exceptions import ClientResponseError
import random

from logging_utils import init_logger, logging

logger = init_logger()

async def get_presence_batch(builder: AsyncRequestBuilder, account_ids, session):
    """account_ids size must be less than or equal to 100"""
    params = {
        'type': 'primary',
        'accountIds': ','.join([str(account_id) for account_id in account_ids])
    }
    response = await builder.get(url="https://m.np.playstation.com/api/userProfile/v1/internal/users/basicPresences",
                                 params=params, session=session)
    return response['basicPresences']

async def get_proxy_request(number=20):
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        
        if response.status_code == 200:
            all_prodivers = ['hola_premium', 'uVPN', 'rayobyte', 'urbanVPN', 'touchVPN', 'aviraVPN', 'nucleusVPN', 'torguard']
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it’s plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list

async def get_proxies():
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)
    
    return all_proxies_sessions

async def run():
    try:
        
        proxy_pool = await get_proxies()
        
        tokens = await get_tokens(number_of_tokens=10)
        authenticator = await Authenticator.async_init(npsso_token=tokens[0], proxy_pool=proxy_pool, logger=logger)
        builder = AsyncRequestBuilder(authenticator)
        print(builder)
        
        # Get a session from the proxy pool
        session = random.choice(proxy_pool)
        account_ids = await get_n_accounts(batch_size=10)
        print("getting result")
        result = await get_presence_batch(builder, account_ids, session)
        result = [r for r in result if r]
        for r in result:
            print(f"{r['accountId']}: {r['availability']}: {r['lastAvailableDate']}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close all proxy sessions
        if proxy_pool:
            for session in proxy_pool:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

async def main():
    await run()

if __name__ == "__main__":
    asyncio.run(main())
