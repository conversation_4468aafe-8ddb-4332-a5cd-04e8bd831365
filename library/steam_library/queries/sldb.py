import query_builder as qb
from library.steam_library.schema.sldb import sldb

class SldbAPI(qb.APISchema):
    class user_games(qb.APIQuery):
        _query=sldb.user_games.select()
        _on_conflict = [qb.OnConflict(
            index_elements=[sldb.user_games.c.pid, sldb.user_games.c.game_id],
            set_={ sldb.user_games.c.play_time: lambda exc: exc.play_time }
        )]

    class active_profiles(qb.APIQuery):
        _query=sldb.active_profiles.select()
        _on_conflict = [qb.OnConflict(
            index_elements=[sldb.active_profiles.c.steam_id],
            set_={ sldb.active_profiles.c.last_updated: lambda exc: exc.last_updated }
        )]

    class account_cookies(qb.APIQuery):
        _query = sldb.account_cookies.select()

    class account_details(qb.APIQuery):
        _query = sldb.account_details.select()

    class game_base(qb.APIQuery):
        _query = sldb.game_base.select()
        _on_conflict = [qb.OnConflict(
            index_elements=[sldb.game_base.c.game_id],
            set_={ sldb.game_base.c.game_name: lambda exc: exc.game_name }
        )]

sldb_api = SldbAPI()