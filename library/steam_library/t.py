import asyncio
import query_builder as qb
from library.steam_library.queries.sldb import sldb_api
import random
from datetime import datetime, timedelta
import pytz
import time

libraries = qb.Schema('sldb', db_name='steam_test')

async def distinct_games():
    distinct_game_ids = await sldb_api.user_games.select(qb.c_.game_id).distinct().fetchall()
    distinct = [id['game_id'] for id in distinct_game_ids]
    existing_game_ids = await sldb_api.game_base.select(qb.c_.game_id).fetchall()
    existing = [id['game_id'] for id in existing_game_ids]

    missing_game_ids = [game_id for game_id in distinct if game_id not in existing]
    print(f"number of user games: {len(distinct)}")
    print(f"number of game base: {len(existing)}")
    print(f"number of missing: {len(missing_game_ids)}")
    for i in range(20):
        print(random.choice(missing_game_ids))

async def get_most_recent_time():
    current_date = datetime.now(tz=pytz.utc)
    last_date = current_date - timedelta(days=1)
    library = await sldb_api.user_games.select(qb.c_.first_seen).where(qb.c_.first_seen > last_date).order_by(qb.c_.first_seen.desc()).fetchone()
    return library


async def get_account_first_seen(pid:int)->datetime:
    earliest_date = datetime.now(tz=pytz.utc)

    library = await sldb_api.user_games.where(qb.c_.pid == int(pid)).fetchall()
    
    for l in library:
        if l['first_seen'] < earliest_date:
            earliest_date = l['first_seen']

    return earliest_date

async def main():
    print(await get_most_recent_time())


if __name__ == "__main__":
    asyncio.run(main())

