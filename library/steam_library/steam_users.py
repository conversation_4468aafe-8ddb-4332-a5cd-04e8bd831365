import query_builder as qb
from datetime import datetime, timedelta
import random
import pandas
import os

steam_metrics = qb.Schema('metrics', db_name='steam')
steam_player = qb.Schema('player', db_name='steam')
steam_db = qb.db(db_name='steam')

async def get_date():
    current_date = datetime.now() - timedelta(days=1)
    return current_date.strftime("%y_%m_%d")


async def get_all_users():
    users = set()
    table_name = f'daily_monitored_{await get_date()}'
    print(f"getting table {table_name}")
    try:
        table = getattr(steam_metrics, table_name)
        account_id_result = qb.Query(table).select('player_pk').distinct().fetchall_sync()
        users.update(r['player_pk'] for r in account_id_result)
    except AttributeError:
        return
    return list(users)    

async def get_all_steam_ids(users):
    batch_size = 65535
    table_name = "player_base"
    table = getattr(steam_player, table_name)
    all_steam_ids = []

    for i in range(0, len(users), batch_size):
        batch = users[i:i + batch_size]
        steam_ids = qb.Query(table).select('steam_id').where(qb.c_.pk.in_(batch)).fetchall_sync()
        all_steam_ids.extend(row['steam_id'] for row in steam_ids if 'steam_id' in row)

    print(len(all_steam_ids))
    return all_steam_ids

async def get_all_ids():
    users = await get_all_users()
    steam_ids = await get_all_steam_ids(users)
    random.shuffle(steam_ids)
    return steam_ids

async def run():
    users = await get_all_users()
    steam_ids = await get_all_steam_ids(users)
    print(steam_ids[:5])

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())
    