import asyncio
import random
import traceback
from pathlib import Path

import playwright
from playwright.async_api import async_playwright
from python_ghost_cursor.playwright_async import create_cursor
from playwright_stealth import stealth_async
from pathlib import Path
import shutil
import json
from datetime import datetime, timedelta
import pytz
from antidetect.antidetect_browser import Antidetect
from library.steam_library.proxies import get_proxies
import query_builder as qb
from library.steam_library.queries.sldb import sldb_api

USER_DATA_DIR = "./tmp/test-user-data-dir-steam"
PROXY_TIMEOUT = 8
libraries = qb.Schema('sldb', db_name='steam_test')
steam_libraries = qb.Schema('library', db_name='steam')

async def clear_user_data_dir():
    dirpath = Path(USER_DATA_DIR)
    if dirpath.exists() and dirpath.is_dir():
        shutil.rmtree(dirpath)

class FailedToLoadInitialPage(Exception):
    pass

async def wait_and_click(page, cursor, selector):
    await page.wait_for_selector(selector)
    await page.click(selector)


async def cookie_interceptor(request, url, future):
    url = "https://steamcommunity.com/profiles/"
    if request.url.startswith(url):
        try:
            headers = await request.all_headers()
            cookie = headers.get("cookie")
            if not cookie:
                raise Exception("Cookie not found")
            if not future.done():
                future.set_result(cookie)
        except playwright._impl._api_types.Error as e:
            print(f"cookie_interceptor: {e}")
        except Exception as e:
            print(f"cookie_interceptor: {e}")

async def login(page, email, username, password):
    print("logging in")
    cursor = create_cursor(page)
    await cursor.random_move()
    start_url = "https://steamcommunity.com/login/home/<USER>"
    try:
        await page.goto(start_url)
    except playwright.async_api.TimeoutError as e:
        print(f"Timeout error when loading homepage: {e}")
        raise FailedToLoadInitialPage
    # await asyncio.sleep(random.uniform(0.2, 2))
    # await wait_and_click(page, cursor, "#global_action_menu > a.global_action_link")
    await asyncio.sleep(random.uniform(0.5, 2))

    print("enter username")
    login_selector = 'input:below(:text("Sign in with account name"))'
    await wait_and_click(page, cursor, login_selector)
    await page.fill(login_selector, username)
    await asyncio.sleep(random.uniform(0.5, 2))

    print("enter password")
    pw_selector = 'input:below(:text("Password"))'
    await wait_and_click(page, cursor, pw_selector)
    await page.fill(pw_selector, password)
    await asyncio.sleep(random.uniform(0.5, 2))

    cookie_future = asyncio.Future()
    cookie_handler = lambda request: cookie_interceptor(
        request, start_url + "profiles", cookie_future
    )
    page.on("request", cookie_handler)
    await asyncio.sleep(random.uniform(2, 5))
    await wait_and_click(page, cursor, 'button:text("Sign in")')
    await asyncio.sleep(random.uniform(2, 5))

    try:
        cookie = await cookie_future
    except Exception as e:
        print(f"Failed to retrieve cookie: {e}")
        return None

    return cookie


async def start_browser(proxy_list, email, username, password):
    while True:
        try:
            host = random.choice(proxy_list)['address']
            proxy = {
                'server': f'{host}:4444',
                'username': "b03f165be1",
                'password': "w1q4cGzT"
            }
            antidetect = await Antidetect().start()
            print("started antidetect")
            browser = await antidetect.new_browser(proxy_string=f"http://{proxy['username']}:{proxy['password']}@{proxy['server']}", headless=True)
            print("started browser")
            page = await browser.new_page()
            await page.goto("https://steamcommunity.com/")
            try:
                cookie = await login(page, email, username, password)
                if cookie:
                    await browser.close()
                    return cookie
                else:
                    print("Failed to retrieve cookie")
            except Exception as e:
                print(f"Error: {e}\n{traceback.format_exc()}")
            finally:
                await browser.close()
            return
        except Exception as e:
            print(e)

async def refresh_cookies(unblocked_accounts):
    proxies = await get_proxies()

    for account in unblocked_accounts:
        account_email = account[0]
        username = account[1].strip()
        password = account[2].strip()
        print(f"getting cookies for {account_email}")
        
        cookie = await start_browser(proxies, account_email, username, password)
        if cookie:
            now = datetime.utcnow().replace(tzinfo=pytz.UTC)
            # updated_cookies.append({'email': account_email, 'cookie': cookie, 'update_time': datetime.datetime.utcnow().replace(tzinfo=pytz.UTC)})
            id = (qb.APIQuery(steam_libraries.account_details).where(email = account_email).fetchone_sync())['id']
            qb.APIQuery(steam_libraries.account_cookies).update_sync({'id': id}, {'last_updated': now})
    # return updated_cookies

async def update_cookies():
    now = datetime.now(tz=pytz.UTC)
    # cookies = await sldb_api.account_cookies.fetchall()
    cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    need_updating = []
    for c in cookies:
        if c['last_updated'] < now - timedelta(hours=PROXY_TIMEOUT):
            need_updating.append(c['account_pk'])
    account_list = []
    for id in need_updating:
        account_info = []
        # a = await sldb_api.account_details.where(id = id).fetchone()
        a = await qb.Query(steam_libraries.account_details).where(id=id).fetchone()
        account_info.append(a['email'])
        account_info.append(a['username'])
        account_info.append(a['password'])
        account_list.append(account_info)
    print("updating cookies")
    await refresh_cookies(account_list)

if __name__ == "__main__":

    # unblocked_accounts = [
    #     ["<EMAIL>","talk_similar_art","Penguinpenpop1"],
    #     ["<EMAIL>","mean_good_man","Penguinpenpop1"],
    #     ["<EMAIL>","open_fine_war","Penguinpenpop1"],
    #     ["<EMAIL>","meet_close_door","Penguinpenpop1"],
    #     ["<EMAIL>","like_young_month","Penguinpenpop1"],

    #     ["<EMAIL>","hold_main_member","Freefoxfire1"],
    #     ["<EMAIL>","reach_hot_power","Freefoxfire1"],
    #     ["<EMAIL>","open_happy_month","Freefoxfire1"],
    #     ["<EMAIL>","see_right_hour","Freefoxfire1"],
    #     ["<EMAIL>", "takedeadhand391", "allowphyA1!"],

    #     ["<EMAIL>", "cutbetterman427", "setlittlA1!"],
    #     ["<EMAIL>", "lovewrongface901", "bringimpA1!"],
    #     ["<EMAIL>", "rundeadnumber653", "waithugeA1!"],
    #     ["<EMAIL>", "couldlatecar517", "expectleA1!"],
    #     ["<EMAIL>", "canopenchild356", "dosurechA1!"],

    #     ["<EMAIL>", "trylowlife536", "setmainlA1!"],
    #     ["<EMAIL>", "letlargeway148", "addpolitA1!"],
    #     ["<EMAIL>", "sitbadresult957", "includeeA1!"],
    #     ["<EMAIL>", "gowhitefamily147", "bebadproA1!"],
    #     ["<EMAIL>", "buycoldgame667", "startentA1!"],

    #     ["<EMAIL>", "loveonlyweek647", "talkmainA1!"],
    #     ["<EMAIL>", "feelmainhour903", "waitstroA1!"],
    #     ["<EMAIL>", "winblueday649", "sitmajorA1!"],
    #     ["<EMAIL>", "readfreeguy661", "buildpopA1!"],
    #     ["<EMAIL>", "gocommonyear207", "usespeciA1!"],

    #     ["<EMAIL>", "shouldhardjob737", "standserA1!"],
    #     ["<EMAIL>", "hearnewlevel885", "befinalwA1!"],
    #     ["<EMAIL>", "needsurehome664", "helpdiffA1!"],
    #     ["<EMAIL>", "sitbadjob618", "keepreadA1!"],
    #     ["<EMAIL>", "trynicewoman757", "getbestsA1!"],

    #     ["<EMAIL>", "holdrightidea972", "mustdemoA1!"],
    #     ["<EMAIL>", "thinkcoldart849", "feelsureA1!"],
    #     ["<EMAIL>", "stopfreeman979", "keepeasyA1!"],
    #     ["<EMAIL>", "knowbestparty640", "loseotheA1!"],
    #     ["<EMAIL>", "playpublicman524", "leadpossA1!"],

    #     ["<EMAIL>", "offershortwar220", "workbettA1!"],
    #     ["<EMAIL>", "telltruename893", "openfullA1!"],
    #     ["<EMAIL>", "getgoodeye797", "buypastiA1!"],
    #     ["<EMAIL>", "usedeadhouse878", "setotherA1!"],
    #     ["<EMAIL>", "mustblueair303", "getwholeA1!"],

    #     ["<EMAIL>", "allowwhiteguy559", "diecertaA1!"],
    #     ["<EMAIL>", "seepublichome356", "spendphyA1!"],
    #     ["<EMAIL>", "seemopenpoint685", "wantgreaA1!"],
    #     ["<EMAIL>", "wantopenhour250", "writeeasA1!"],
    #     ["<EMAIL>", "findwronghour251", "askbadboA1!"],

    #     ["<EMAIL>", "looklowstudy961", "understaA1!"],
    #     ["<EMAIL>", "showhardthing572", "sithappyA1!"],
    #     ["<EMAIL>", "setlateworld463", "sendhugeA1!"],
    #     ["<EMAIL>", "remainhotway316", "turnrealA1!"],
    #     ["<EMAIL>", "useearlybody465", "turngreeA1!"],

    #     ["<EMAIL>", "cutredfact820", "expectpeA1!"],
    # ]
    # asyncio.run(refresh_cookies(unblocked_accounts))
    asyncio.run(update_cookies())