#gets a snapshot of the library for n users in the user_games table
import query_builder as qb
import random
import asyncio

steam_libraries = qb.Schema('library', db_name='steam')

async def get_random_users(n=100):
    query = (
        qb.Query(steam_libraries.active_profiles)
        .select(qb.c_.steam_id)
        .distinct()
    )
    users = await query.fetchall()
    users = random.sample(users, n)
    return [u['steam_id'] for u in users]

async def save_snapshot(snapshot:list):
    with open('library/steam_library/20250701_snapshot3.tsv', 'w') as f:
        f.write("pid\tgame_id\tplay_time\n")
        for s in snapshot:
            f.write(f"{s['pid']}\t{s['game_id']}\t{s['play_time']}\n")

async def save_sample(users:list):
    with open('library/steam_library/sample.tsv', 'w') as f:
        f.write("steam_id\n")
        for u in users:
            f.write(f"{u}\n")

async def load_sample():
    with open('library/steam_library/sample3.tsv', 'r') as f:
        users = f.read().splitlines()
    return users

async def get_snapshot(users:list):
    query = (
        qb.Query(steam_libraries.user_games)
        .select(qb.c_.pid, qb.c_.game_id, qb.c_.play_time)
        .where(qb.c_.pid.in_(users))
    )
    snapshot = await query.fetchall()
    return snapshot

async def main(use_sample=True):
    if use_sample:
        users = await load_sample()
        users = users[1:]
        users = [int(u) for u in users]
    else:
        users = await get_random_users(1000)
        await save_sample(users)

    snapshot = await get_snapshot(users)
    await save_snapshot(snapshot)

if __name__ == "__main__":
    asyncio.run(main(True))